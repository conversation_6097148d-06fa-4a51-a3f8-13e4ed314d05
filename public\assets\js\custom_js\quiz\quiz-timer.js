/**
 * Quiz Timer Functionality
 * Handles countdown timer and auto-submission
 */

class QuizTimer {
    constructor(remainingSeconds, onTimeUp) {
        this.remainingSeconds = remainingSeconds;
        this.onTimeUp = onTimeUp;
        this.timerElement = document.getElementById('timer');
        this.interval = null;
        this.isRunning = false;
    }

    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.updateDisplay();
        
        this.interval = setInterval(() => {
            this.remainingSeconds--;
            this.updateDisplay();
            
            if (this.remainingSeconds <= 0) {
                this.stop();
                this.onTimeUp();
            }
        }, 1000);
    }

    stop() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        this.isRunning = false;
    }

    updateDisplay() {
        const minutes = Math.floor(this.remainingSeconds / 60);
        const seconds = this.remainingSeconds % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (this.timerElement) {
            this.timerElement.textContent = timeString;
            
            // Add warning classes based on remaining time
            this.timerElement.classList.remove('warning', 'danger');
            
            if (this.remainingSeconds <= 60) {
                this.timerElement.classList.add('danger');
            } else if (this.remainingSeconds <= 300) { // 5 minutes
                this.timerElement.classList.add('warning');
            }
        }
    }

    getRemainingSeconds() {
        return this.remainingSeconds;
    }
}

// Export for use in other scripts
window.QuizTimer = QuizTimer;
