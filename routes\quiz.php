<?php

use App\Http\Controllers\Quiz\QuizCandidateController;
use Illuminate\Support\Facades\Route;

/* ==============================================
===============< Public Quiz Routes >==========
===============================================*/
Route::controller(QuizCandidateController::class)
    ->prefix('quiz')
    ->name('quiz.')
    ->group(function () {
        // Public quiz routes (no authentication required)
        Route::get('/start', 'start')->name('start');
        Route::post('/start', 'startQuiz')->name('start.post');
        Route::get('/{session}', 'quiz')->name('quiz');
        Route::get('/{session}/result', 'result')->name('result');
        
        // AJAX routes
        Route::post('/submit-answer', 'submitAnswer')->name('submit.answer');
        Route::post('/complete', 'complete')->name('complete');
        Route::post('/check-session', 'checkSession')->name('check.session');
    });
