<?php

use App\Http\Controllers\Administration\Quiz\QuizController;
use Illuminate\Support\Facades\Route;

/* ==============================================
===============< Quiz Routes >================
===============================================*/
Route::controller(QuizController::class)
    ->prefix('quiz')
    ->name('quiz.')
    ->group(function () {
        // Quiz Questions Management
        Route::get('/questions', 'index')->name('index')->can('Quiz Everything');
        Route::get('/questions/create', 'create')->name('create')->can('Quiz Create');
        Route::post('/questions/store', 'store')->name('store')->can('Quiz Create');
        Route::get('/questions/show/{quiz}', 'show')->name('show')->can('Quiz Read');
        Route::get('/questions/edit/{quiz}', 'edit')->name('edit')->can('Quiz Update');
        Route::put('/questions/update/{quiz}', 'update')->name('update')->can('Quiz Update');
        Route::delete('/questions/destroy/{quiz}', 'destroy')->name('destroy')->can('Quiz Delete');
        
        // Quiz Sessions Management
        Route::get('/sessions', 'sessions')->name('sessions')->can('Quiz Everything');
        Route::get('/sessions/show/{session}', 'sessionShow')->name('session.show')->can('Quiz Read');
    });
