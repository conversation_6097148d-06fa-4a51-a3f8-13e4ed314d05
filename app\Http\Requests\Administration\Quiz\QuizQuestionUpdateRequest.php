<?php

namespace App\Http\Requests\Administration\Quiz;

use App\Models\Quiz\QuizQuestion;
use Illuminate\Foundation\Http\FormRequest;

class QuizQuestionUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $answerOptions = QuizQuestion::getAnswerOptions();
        $statusOptions = QuizQuestion::getStatusOptions();
        
        return [
            'question' => [
                'required',
                'string',
                'min:10',
                'max:1000'
            ],
            'option_a' => [
                'required',
                'string',
                'max:255'
            ],
            'option_b' => [
                'required',
                'string',
                'max:255'
            ],
            'option_c' => [
                'required',
                'string',
                'max:255'
            ],
            'option_d' => [
                'required',
                'string',
                'max:255'
            ],
            'correct_answer' => [
                'required',
                'in:' . implode(',', $answerOptions)
            ],
            'status' => [
                'required',
                'in:' . implode(',', $statusOptions)
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'question.required' => 'The question field is required.',
            'question.min' => 'The question must be at least 10 characters.',
            'question.max' => 'The question may not be greater than 1000 characters.',
            'option_a.required' => 'Option A is required.',
            'option_b.required' => 'Option B is required.',
            'option_c.required' => 'Option C is required.',
            'option_d.required' => 'Option D is required.',
            'correct_answer.required' => 'Please select the correct answer.',
            'correct_answer.in' => 'The correct answer must be one of: A, B, C, D.',
            'status.required' => 'Please select a status.',
            'status.in' => 'The status must be either Active or Inactive.',
        ];
    }
}
