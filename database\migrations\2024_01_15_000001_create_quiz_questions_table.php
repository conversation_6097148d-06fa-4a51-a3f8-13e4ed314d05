<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_questions', function (Blueprint $table) {
            $table->id();
            
            $table->text('question')
                ->comment('The quiz question text');
            
            $table->string('option_a')
                ->comment('Option A');
            
            $table->string('option_b')
                ->comment('Option B');
            
            $table->string('option_c')
                ->comment('Option C');
            
            $table->string('option_d')
                ->comment('Option D');
            
            $table->enum('correct_answer', ['A', 'B', 'C', 'D'])
                ->comment('The correct answer option');
            
            $table->enum('status', ['Active', 'Inactive'])
                ->default('Active')
                ->comment('Question status');
            
            $table->foreignId('creator_id')
                ->constrained('users')
                ->onUpdate('cascade')
                ->onDelete('cascade')
                ->comment('User who created the question');
            
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['status', 'deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_questions');
    }
};
