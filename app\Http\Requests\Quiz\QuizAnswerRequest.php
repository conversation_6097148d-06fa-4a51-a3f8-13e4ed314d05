<?php

namespace App\Http\Requests\Quiz;

use App\Models\Quiz\QuizQuestion;
use Illuminate\Foundation\Http\FormRequest;

class QuizAnswerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $answerOptions = QuizQuestion::getAnswerOptions();
        
        return [
            'session_id' => [
                'required',
                'string',
                'exists:quiz_sessions,session_id'
            ],
            'question_id' => [
                'required',
                'integer',
                'exists:quiz_questions,id'
            ],
            'selected_answer' => [
                'required',
                'in:' . implode(',', $answerOptions)
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'session_id.required' => 'Session ID is required.',
            'session_id.exists' => 'Invalid session.',
            'question_id.required' => 'Question ID is required.',
            'question_id.exists' => 'Invalid question.',
            'selected_answer.required' => 'Please select an answer.',
            'selected_answer.in' => 'Invalid answer option.',
        ];
    }
}
