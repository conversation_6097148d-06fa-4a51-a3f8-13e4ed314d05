<?php

namespace App\Models\Quiz;

use App\Traits\HasCustomRouteId;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use App\Models\Quiz\Relations\QuizSessionRelations;
use App\Models\Quiz\Accessors\QuizSessionAccessors;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class QuizSession extends Model
{
    use HasFactory, SoftDeletes, CascadeSoftDeletes, HasCustomRouteId;

    // Relations
    use QuizSessionRelations;

    // Accessors
    use QuizSessionAccessors;

    protected $cascadeDeletes = ['answers'];

    // Mass assignable attributes
    protected $fillable = [
        'session_id',
        'candidate_name',
        'candidate_email',
        'question_ids',
        'total_score',
        'status',
        'started_at',
        'completed_at',
        'expires_at',
    ];

    // Casting attributes
    protected $casts = [
        'question_ids' => 'array',
        'total_score' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the status options
     */
    public static function getStatusOptions(): array
    {
        return ['Active', 'Completed', 'Expired'];
    }

    /**
     * Scope to get only active sessions
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    /**
     * Scope to get completed sessions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    /**
     * Check if the session is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && now()->isAfter($this->expires_at);
    }

    /**
     * Check if the session is active
     */
    public function isActive(): bool
    {
        return $this->status === 'Active' && !$this->isExpired();
    }

    /**
     * Mark session as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'Completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark session as expired
     */
    public function markAsExpired(): void
    {
        $this->update([
            'status' => 'Expired',
        ]);
    }

    /**
     * Get remaining time in seconds
     */
    public function getRemainingTimeInSeconds(): int
    {
        if (!$this->expires_at) {
            return 0;
        }

        $remaining = $this->expires_at->diffInSeconds(now(), false);
        return max(0, -$remaining);
    }
}
