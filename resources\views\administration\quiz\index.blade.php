@extends('layouts.administration.app')

@section('page_title', 'Quiz Questions')

@section('content')
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar flex-shrink-0 me-3">
                        <span class="avatar-initial rounded bg-label-primary">
                            <i class="ti ti-help"></i>
                        </span>
                    </div>
                    <div>
                        <small class="text-muted d-block">Total Questions</small>
                        <div class="d-flex align-items-center">
                            <h6 class="mb-0 me-1">{{ $statistics['total_questions'] }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar flex-shrink-0 me-3">
                        <span class="avatar-initial rounded bg-label-success">
                            <i class="ti ti-check"></i>
                        </span>
                    </div>
                    <div>
                        <small class="text-muted d-block">Active Questions</small>
                        <div class="d-flex align-items-center">
                            <h6 class="mb-0 me-1">{{ $statistics['active_questions'] }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar flex-shrink-0 me-3">
                        <span class="avatar-initial rounded bg-label-info">
                            <i class="ti ti-users"></i>
                        </span>
                    </div>
                    <div>
                        <small class="text-muted d-block">Total Sessions</small>
                        <div class="d-flex align-items-center">
                            <h6 class="mb-0 me-1">{{ $statistics['total_sessions'] }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="avatar flex-shrink-0 me-3">
                        <span class="avatar-initial rounded bg-label-warning">
                            <i class="ti ti-chart-line"></i>
                        </span>
                    </div>
                    <div>
                        <small class="text-muted d-block">Average Score</small>
                        <div class="d-flex align-items-center">
                            <h6 class="mb-0 me-1">{{ number_format($statistics['average_score'], 1) }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quiz Questions Management -->
<div class="card">
    <div class="card-header header-elements">
        <h5 class="mb-0">Quiz Questions Management</h5>
        <div class="card-header-elements ms-auto">
            <a href="{{ route('administration.quiz.create') }}" class="btn btn-primary">
                <i class="ti ti-plus me-1"></i>Add Question
            </a>
            <a href="{{ route('administration.quiz.sessions') }}" class="btn btn-info ms-2">
                <i class="ti ti-users me-1"></i>View Sessions
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card-body border-bottom">
        <form method="GET" action="{{ route('administration.quiz.index') }}" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="Active" {{ request('status') === 'Active' ? 'selected' : '' }}>Active</option>
                    <option value="Inactive" {{ request('status') === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Creator</label>
                <select name="creator_id" class="form-select">
                    <option value="">All Creators</option>
                    @foreach($creators as $creator)
                        <option value="{{ $creator->id }}" {{ request('creator_id') == $creator->id ? 'selected' : '' }}>
                            {{ $creator->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Search</label>
                <input type="text" name="search" class="form-control" placeholder="Search questions..." value="{{ request('search') }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-search"></i>
                    </button>
                    <a href="{{ route('administration.quiz.index') }}" class="btn btn-outline-secondary">
                        <i class="ti ti-x"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>{{ __('SL') }}</th>
                        <th>{{ __('Question') }}</th>
                        <th>{{ __('Correct Answer') }}</th>
                        <th>{{ __('Status') }}</th>
                        <th>{{ __('Creator') }}</th>
                        <th>{{ __('Created') }}</th>
                        <th class="text-center">{{ __('Actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($questions as $question)
                        <tr>
                            <td>{{ $loop->iteration + ($questions->currentPage() - 1) * $questions->perPage() }}</td>
                            <td>
                                <div style="max-width: 300px;">
                                    {{ Str::limit($question->question, 100) }}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ $question->correct_answer }}</span>
                                {{ Str::limit($question->options[$question->correct_answer], 50) }}
                            </td>
                            <td>
                                <span class="badge {{ $question->status === 'Active' ? 'bg-success' : 'bg-secondary' }}">
                                    {{ $question->status }}
                                </span>
                            </td>
                            <td>{{ $question->creator->name }}</td>
                            <td>{{ $question->created_at->format('M d, Y') }}</td>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="ti ti-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="{{ route('administration.quiz.show', $question) }}">
                                            <i class="ti ti-eye me-1"></i> View
                                        </a>
                                        @can('Quiz Update')
                                            <a class="dropdown-item" href="{{ route('administration.quiz.edit', $question) }}">
                                                <i class="ti ti-pencil me-1"></i> Edit
                                            </a>
                                        @endcan
                                        @can('Quiz Delete')
                                            <form action="{{ route('administration.quiz.destroy', $question) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Are you sure you want to delete this question?')">
                                                    <i class="ti ti-trash me-1"></i> Delete
                                                </button>
                                            </form>
                                        @endcan
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="ti ti-help-circle mb-2" style="font-size: 3rem; color: #ddd;"></i>
                                    <h6 class="text-muted">No questions found</h6>
                                    <p class="text-muted mb-3">Start by creating your first quiz question</p>
                                    <a href="{{ route('administration.quiz.create') }}" class="btn btn-primary">
                                        <i class="ti ti-plus me-1"></i>Add Question
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($questions->hasPages())
            <div class="mt-3">
                {{ $questions->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
