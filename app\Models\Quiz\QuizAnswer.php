<?php

namespace App\Models\Quiz;

use Illuminate\Database\Eloquent\Model;
use App\Models\Quiz\Relations\QuizAnswerRelations;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class QuizAnswer extends Model
{
    use HasFactory;

    // Relations
    use QuizAnswerRelations;

    // Mass assignable attributes
    protected $fillable = [
        'quiz_session_id',
        'quiz_question_id',
        'selected_answer',
        'is_correct',
        'score',
    ];

    // Casting attributes
    protected $casts = [
        'is_correct' => 'boolean',
        'score' => 'integer',
    ];

    /**
     * Scope to get correct answers
     */
    public function scopeCorrect($query)
    {
        return $query->where('is_correct', true);
    }

    /**
     * Scope to get incorrect answers
     */
    public function scopeIncorrect($query)
    {
        return $query->where('is_correct', false);
    }
}
