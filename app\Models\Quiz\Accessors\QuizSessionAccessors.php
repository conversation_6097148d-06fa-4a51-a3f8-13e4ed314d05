<?php

namespace App\Models\Quiz\Accessors;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait QuizSessionAccessors
{
    /**
     * Get the formatted duration of the quiz
     */
    protected function durationFormatted(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->started_at || !$this->completed_at) {
                    return 'N/A';
                }

                $duration = $this->completed_at->diffInSeconds($this->started_at);
                $minutes = intval($duration / 60);
                $seconds = $duration % 60;

                return sprintf('%02d:%02d', $minutes, $seconds);
            }
        );
    }

    /**
     * Get the percentage score
     */
    protected function scorePercentage(): Attribute
    {
        return Attribute::make(
            get: function () {
                $totalQuestions = count($this->question_ids ?? []);
                if ($totalQuestions === 0) {
                    return 0;
                }

                // Calculate percentage based on correct answers
                $correctAnswers = $this->answers()->where('is_correct', true)->count();
                return round(($correctAnswers / $totalQuestions) * 100, 2);
            }
        );
    }

    /**
     * Get the result status (Pass/Fail)
     */
    protected function resultStatus(): Attribute
    {
        return Attribute::make(
            get: function () {
                $percentage = $this->score_percentage;
                return $percentage >= 60 ? 'Pass' : 'Fail';
            }
        );
    }

    /**
     * Get the number of correct answers
     */
    protected function correctAnswersCount(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->answers()->where('is_correct', true)->count();
            }
        );
    }

    /**
     * Get the number of incorrect answers
     */
    protected function incorrectAnswersCount(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->answers()->where('is_correct', false)->count();
            }
        );
    }

    /**
     * Get the total number of questions
     */
    protected function totalQuestionsCount(): Attribute
    {
        return Attribute::make(
            get: function () {
                return count($this->question_ids ?? []);
            }
        );
    }
}
