@extends('layouts.quiz')

@section('page_title', 'Start Quiz')

@section('content')
<!-- Quiz Start Card -->
<div class="card">
    <div class="card-body">
        <div class="app-brand justify-content-center mb-4">
            <a href="{{ route('quiz.start') }}" class="app-brand-link gap-2">
                <span class="app-brand-logo demo">
                    <i class="ti ti-brain text-primary" style="font-size: 2rem;"></i>
                </span>
                <span class="app-brand-text demo text-body fw-bold">{{ config('app.name') }}</span>
            </a>
        </div>

        <h4 class="mb-2 text-center">Welcome to the Quiz!</h4>
        <p class="mb-4 text-center text-muted">Please enter your information to start the quiz</p>

        <!-- Quiz Instructions -->
        <div class="alert alert-info mb-4">
            <h6 class="alert-heading mb-2">
                <i class="ti ti-info-circle me-2"></i>Quiz Instructions
            </h6>
            <ul class="mb-0 ps-3">
                <li>You will have <strong>10 minutes</strong> to complete the quiz</li>
                <li>The quiz contains <strong>10 multiple-choice questions</strong></li>
                <li>Each correct answer gives you <strong>+1 point</strong></li>
                <li>Each wrong answer gives you <strong>-1 point</strong></li>
                <li>You can change your answers before submitting</li>
                <li>The quiz will auto-submit when time expires</li>
            </ul>
        </div>

        <form method="POST" action="{{ route('quiz.start.post') }}" id="quizStartForm">
            @csrf
            
            <div class="mb-3">
                <label for="candidate_name" class="form-label">Full Name</label>
                <input 
                    type="text" 
                    class="form-control @error('candidate_name') is-invalid @enderror" 
                    id="candidate_name" 
                    name="candidate_name" 
                    placeholder="Enter your full name"
                    value="{{ old('candidate_name', request()->cookie('candidate_name')) }}"
                    required
                    autofocus
                />
                @error('candidate_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-4">
                <label for="candidate_email" class="form-label">Email Address</label>
                <input 
                    type="email" 
                    class="form-control @error('candidate_email') is-invalid @enderror" 
                    id="candidate_email" 
                    name="candidate_email" 
                    placeholder="Enter your email address"
                    value="{{ old('candidate_email', request()->cookie('candidate_email')) }}"
                    required
                />
                @error('candidate_email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <button type="submit" class="btn btn-primary d-grid w-100 mb-3" id="startQuizBtn">
                <i class="ti ti-play me-2"></i>Start Quiz
            </button>
        </form>

        <div class="text-center">
            <small class="text-muted">
                Make sure you have a stable internet connection before starting.
            </small>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#quizStartForm').on('submit', function() {
        $('#startQuizBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Starting...');
    });
});
</script>
@endpush
