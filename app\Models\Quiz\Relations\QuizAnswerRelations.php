<?php

namespace App\Models\Quiz\Relations;

use App\Models\Quiz\QuizSession;
use App\Models\Quiz\QuizQuestion;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait QuizAnswerRelations
{
    /**
     * Get the quiz session this answer belongs to.
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(QuizSession::class, 'quiz_session_id');
    }

    /**
     * Get the quiz question this answer is for.
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(QuizQuestion::class, 'quiz_question_id');
    }
}
