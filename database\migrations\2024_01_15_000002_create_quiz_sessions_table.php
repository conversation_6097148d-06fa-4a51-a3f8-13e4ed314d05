<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_sessions', function (Blueprint $table) {
            $table->id();
            
            $table->string('session_id')
                ->unique()
                ->comment('Unique session identifier');
            
            $table->string('candidate_name')
                ->comment('Candidate name');
            
            $table->string('candidate_email')
                ->comment('Candidate email');
            
            $table->json('question_ids')
                ->comment('Array of question IDs for this session');
            
            $table->integer('total_score')
                ->default(0)
                ->comment('Total score achieved');
            
            $table->enum('status', ['Active', 'Completed', 'Expired'])
                ->default('Active')
                ->comment('Session status');
            
            $table->timestamp('started_at')
                ->nullable()
                ->comment('When the quiz was started');
            
            $table->timestamp('completed_at')
                ->nullable()
                ->comment('When the quiz was completed');
            
            $table->timestamp('expires_at')
                ->nullable()
                ->comment('When the session expires');
            
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['session_id', 'status']);
            $table->index(['candidate_email', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_sessions');
    }
};
