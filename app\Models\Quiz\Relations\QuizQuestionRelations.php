<?php

namespace App\Models\Quiz\Relations;

use App\Models\User;
use App\Models\Quiz\QuizAnswer;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait QuizQuestionRelations
{
    /**
     * Get the user who created the question.
     */
    public function creator(): Bel<PERSON><PERSON>T<PERSON>
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get all answers for this question.
     */
    public function answers(): HasM<PERSON>
    {
        return $this->hasMany(QuizAnswer::class);
    }
}
