<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_answers', function (Blueprint $table) {
            $table->id();
            
            $table->foreignId('quiz_session_id')
                ->constrained()
                ->onUpdate('cascade')
                ->onDelete('cascade')
                ->comment('Related quiz session');
            
            $table->foreignId('quiz_question_id')
                ->constrained()
                ->onUpdate('cascade')
                ->onDelete('cascade')
                ->comment('Related quiz question');
            
            $table->enum('selected_answer', ['A', 'B', 'C', 'D'])
                ->comment('Answer selected by candidate');
            
            $table->boolean('is_correct')
                ->comment('Whether the answer is correct');
            
            $table->integer('score')
                ->comment('Score for this answer (+1 or -1)');
            
            $table->timestamps();
            
            $table->unique(['quiz_session_id', 'quiz_question_id']);
            $table->index(['quiz_session_id', 'is_correct']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_answers');
    }
};
