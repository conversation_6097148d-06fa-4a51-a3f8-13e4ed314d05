@extends('layouts.quiz')

@section('page_title', 'Quiz Results')

@push('styles')
<style>
.result-card {
    border: 2px solid #e7eaf3;
    transition: all 0.3s ease;
}
.result-card.pass {
    border-color: #28a745;
    background-color: #f8fff9;
}
.result-card.fail {
    border-color: #dc3545;
    background-color: #fff8f8;
}
.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto;
}
.score-circle.pass {
    background-color: #28a745;
    color: white;
}
.score-circle.fail {
    background-color: #dc3545;
    color: white;
}
.answer-review {
    max-height: 400px;
    overflow-y: auto;
}
.answer-item {
    border-left: 4px solid #e7eaf3;
    padding-left: 15px;
    margin-bottom: 15px;
}
.answer-item.correct {
    border-left-color: #28a745;
}
.answer-item.incorrect {
    border-left-color: #dc3545;
}
</style>
@endpush

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Result Summary -->
        <div class="card result-card {{ $quizSession->result_status === 'Pass' ? 'pass' : 'fail' }} mb-4">
            <div class="card-body text-center">
                <div class="app-brand justify-content-center mb-4">
                    <span class="app-brand-logo demo">
                        <i class="ti ti-brain text-primary" style="font-size: 2rem;"></i>
                    </span>
                    <span class="app-brand-text demo text-body fw-bold ms-2">{{ config('app.name') }}</span>
                </div>

                <h4 class="mb-3">Quiz Completed!</h4>
                
                <div class="score-circle {{ $quizSession->result_status === 'Pass' ? 'pass' : 'fail' }} mb-4">
                    {{ $quizSession->score_percentage }}%
                </div>

                <h5 class="mb-2">
                    @if($quizSession->result_status === 'Pass')
                        <i class="ti ti-check-circle text-success me-2"></i>Congratulations! You Passed
                    @else
                        <i class="ti ti-x-circle text-danger me-2"></i>Better Luck Next Time
                    @endif
                </h5>

                <div class="row text-center mt-4">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">Total Questions</h6>
                            <h4 class="mb-0">{{ $quizSession->total_questions_count }}</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">Correct Answers</h6>
                            <h4 class="mb-0 text-success">{{ $quizSession->correct_answers_count }}</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">Wrong Answers</h6>
                            <h4 class="mb-0 text-danger">{{ $quizSession->incorrect_answers_count }}</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-1">Duration</h6>
                        <h4 class="mb-0">{{ $quizSession->duration_formatted }}</h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- Candidate Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="ti ti-user me-2"></i>Candidate Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-2"><strong>Name:</strong> {{ $quizSession->candidate_name }}</p>
                        <p class="mb-2"><strong>Email:</strong> {{ $quizSession->candidate_email }}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-2"><strong>Started At:</strong> {{ $quizSession->started_at->format('M d, Y h:i A') }}</p>
                        <p class="mb-2"><strong>Completed At:</strong> {{ $quizSession->completed_at->format('M d, Y h:i A') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Answer Review -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="ti ti-list-details me-2"></i>Answer Review
                </h5>
            </div>
            <div class="card-body">
                <div class="answer-review">
                    @foreach($quizSession->answers as $index => $answer)
                    <div class="answer-item {{ $answer->is_correct ? 'correct' : 'incorrect' }}">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-1">Question {{ $index + 1 }}</h6>
                            <span class="badge {{ $answer->is_correct ? 'bg-success' : 'bg-danger' }}">
                                {{ $answer->is_correct ? 'Correct' : 'Incorrect' }}
                            </span>
                        </div>
                        
                        <p class="mb-2">{{ $answer->question->question }}</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">Your Answer:</small>
                                <p class="mb-1">
                                    <span class="badge bg-primary me-2">{{ $answer->selected_answer }}</span>
                                    {{ $answer->question->options[$answer->selected_answer] }}
                                </p>
                            </div>
                            @if(!$answer->is_correct)
                            <div class="col-md-6">
                                <small class="text-muted">Correct Answer:</small>
                                <p class="mb-1">
                                    <span class="badge bg-success me-2">{{ $answer->question->correct_answer }}</span>
                                    {{ $answer->question->options[$answer->question->correct_answer] }}
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="text-center mt-4 mb-4">
            <a href="{{ route('quiz.start') }}" class="btn btn-primary">
                <i class="ti ti-refresh me-2"></i>Take Another Quiz
            </a>
        </div>
    </div>
</div>
@endsection
