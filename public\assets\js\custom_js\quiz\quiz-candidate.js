/**
 * Quiz Candidate Interface
 * Handles answer selection, submission, and quiz completion
 */

$(document).ready(function() {
    'use strict';

    // Quiz configuration
    const sessionId = $('#sessionId').val();
    const initialRemainingTime = parseInt($('#remainingTime').val());
    let selectedAnswers = {};
    let isSubmitting = false;

    // CSRF token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize timer
    const timer = new QuizTimer(initialRemainingTime, function() {
        // Auto-submit when time is up
        autoSubmitQuiz();
    });

    timer.start();

    // Check session status periodically
    const sessionCheckInterval = setInterval(checkSessionStatus, 30000); // Every 30 seconds

    // Handle option selection
    $('.option-card').on('click', function() {
        const questionId = $(this).data('question-id');
        const selectedOption = $(this).data('option');
        
        // Remove selection from other options in the same question
        $(`.option-card[data-question-id="${questionId}"]`).removeClass('selected');
        
        // Add selection to clicked option
        $(this).addClass('selected');
        
        // Store the answer
        selectedAnswers[questionId] = selectedOption;
        
        // Mark question as answered
        $(`#question-${questionId}`).addClass('answered');
        
        // Update progress
        updateProgress();
        
        // Submit answer via AJAX
        submitAnswer(questionId, selectedOption);
    });

    // Handle quiz submission
    $('#submitQuizBtn').on('click', function() {
        if (isSubmitting) return;
        
        const answeredCount = Object.keys(selectedAnswers).length;
        const totalQuestions = $('.question-card').length;
        
        if (answeredCount < totalQuestions) {
            Swal.fire({
                title: 'Incomplete Quiz',
                text: `You have answered ${answeredCount} out of ${totalQuestions} questions. Do you want to submit anyway?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, Submit',
                cancelButtonText: 'Continue Quiz',
                customClass: {
                    confirmButton: 'btn btn-warning me-3',
                    cancelButton: 'btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    submitQuiz();
                }
            });
        } else {
            Swal.fire({
                title: 'Submit Quiz?',
                text: 'Are you sure you want to submit your quiz? This action cannot be undone.',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, Submit',
                cancelButtonText: 'Review Answers',
                customClass: {
                    confirmButton: 'btn btn-success me-3',
                    cancelButton: 'btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    submitQuiz();
                }
            });
        }
    });

    // Submit individual answer
    function submitAnswer(questionId, selectedOption) {
        $.ajax({
            url: '/quiz/submit-answer',
            method: 'POST',
            data: {
                session_id: sessionId,
                question_id: questionId,
                selected_answer: selectedOption
            },
            success: function(response) {
                if (response.success) {
                    console.log('Answer submitted successfully');
                } else {
                    console.error('Failed to submit answer:', response.message);
                }
            },
            error: function(xhr) {
                console.error('Error submitting answer:', xhr.responseJSON?.message || 'Unknown error');
                
                // Show error to user if it's a critical error
                if (xhr.status === 400 && xhr.responseJSON?.message) {
                    Swal.fire({
                        title: 'Error',
                        text: xhr.responseJSON.message,
                        icon: 'error',
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });
                }
            }
        });
    }

    // Submit entire quiz
    function submitQuiz() {
        if (isSubmitting) return;
        
        isSubmitting = true;
        timer.stop();
        clearInterval(sessionCheckInterval);
        
        // Disable submit button
        $('#submitQuizBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Submitting...');
        
        $.ajax({
            url: '/quiz/complete',
            method: 'POST',
            data: {
                session_id: sessionId
            },
            success: function(response) {
                if (response.success) {
                    window.location.href = response.redirect_url;
                } else {
                    handleSubmissionError(response.message || 'Failed to submit quiz');
                }
            },
            error: function(xhr) {
                handleSubmissionError(xhr.responseJSON?.message || 'Failed to submit quiz');
            }
        });
    }

    // Auto-submit when time is up
    function autoSubmitQuiz() {
        if (isSubmitting) return;
        
        Swal.fire({
            title: 'Time\'s Up!',
            text: 'Your quiz time has expired. Submitting your answers...',
            icon: 'warning',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            customClass: {
                popup: 'swal2-no-animation'
            }
        });
        
        setTimeout(() => {
            submitQuiz();
        }, 2000);
    }

    // Check session status
    function checkSessionStatus() {
        if (isSubmitting) return;
        
        $.ajax({
            url: '/quiz/check-session',
            method: 'POST',
            data: {
                session_id: sessionId
            },
            success: function(response) {
                if (!response.success || response.expired) {
                    timer.stop();
                    clearInterval(sessionCheckInterval);
                    
                    Swal.fire({
                        title: 'Session Expired',
                        text: 'Your quiz session has expired. Redirecting to results...',
                        icon: 'warning',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false
                    });
                    
                    setTimeout(() => {
                        window.location.href = response.redirect_url;
                    }, 2000);
                }
            },
            error: function() {
                // Silently handle session check errors
                console.warn('Failed to check session status');
            }
        });
    }

    // Update progress indicator
    function updateProgress() {
        const answeredCount = Object.keys(selectedAnswers).length;
        const totalQuestions = $('.question-card').length;
        const percentage = (answeredCount / totalQuestions) * 100;
        
        $('#progressBar').css('width', percentage + '%');
        $('#answeredCount').text(answeredCount);
    }

    // Handle submission errors
    function handleSubmissionError(message) {
        isSubmitting = false;
        
        // Re-enable submit button
        $('#submitQuizBtn').prop('disabled', false).html('<i class="ti ti-check me-2"></i>Submit Quiz');
        
        Swal.fire({
            title: 'Submission Failed',
            text: message,
            icon: 'error',
            customClass: {
                confirmButton: 'btn btn-primary'
            }
        });
    }

    // Prevent page refresh/navigation during quiz
    window.addEventListener('beforeunload', function(e) {
        if (!isSubmitting && timer.getRemainingSeconds() > 0) {
            e.preventDefault();
            e.returnValue = 'Are you sure you want to leave? Your quiz progress will be lost.';
            return e.returnValue;
        }
    });

    // Prevent right-click context menu (optional security measure)
    $(document).on('contextmenu', function(e) {
        e.preventDefault();
    });

    // Prevent text selection (optional security measure)
    $(document).on('selectstart', function(e) {
        e.preventDefault();
    });
});
