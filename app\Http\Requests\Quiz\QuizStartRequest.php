<?php

namespace App\Http\Requests\Quiz;

use Illuminate\Foundation\Http\FormRequest;

class QuizStartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'candidate_name' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-zA-Z\s]+$/'
            ],
            'candidate_email' => [
                'required',
                'email',
                'max:255'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'candidate_name.required' => 'Please enter your name.',
            'candidate_name.min' => 'Name must be at least 2 characters.',
            'candidate_name.max' => 'Name may not be greater than 100 characters.',
            'candidate_name.regex' => 'Name should only contain letters and spaces.',
            'candidate_email.required' => 'Please enter your email address.',
            'candidate_email.email' => 'Please enter a valid email address.',
            'candidate_email.max' => 'Email may not be greater than 255 characters.',
        ];
    }
}
