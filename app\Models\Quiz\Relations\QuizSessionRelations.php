<?php

namespace App\Models\Quiz\Relations;

use App\Models\Quiz\QuizAnswer;
use App\Models\Quiz\QuizQuestion;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait QuizSessionRelations
{
    /**
     * Get all answers for this session.
     */
    public function answers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(QuizAnswer::class);
    }

    /**
     * Get the questions for this session.
     */
    public function questions(): BelongsToMany
    {
        return $this->belongsToMany(QuizQuestion::class, 'quiz_answers', 'quiz_session_id', 'quiz_question_id')
                    ->withPivot(['selected_answer', 'is_correct', 'score'])
                    ->withTimestamps();
    }
}
