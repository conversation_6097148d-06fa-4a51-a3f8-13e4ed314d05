@extends('layouts.administration.app')

@section('page_title', 'Create Quiz Question')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header header-elements">
                <h5 class="mb-0">Create New Quiz Question</h5>
                <div class="card-header-elements ms-auto">
                    <a href="{{ route('administration.quiz.index') }}" class="btn btn-outline-secondary">
                        <i class="ti ti-arrow-left me-1"></i>Back to Questions
                    </a>
                </div>
            </div>

            <div class="card-body">
                <form action="{{ route('administration.quiz.store') }}" method="POST">
                    @csrf

                    <!-- Question -->
                    <div class="mb-4">
                        <label for="question" class="form-label">Question <span class="text-danger">*</span></label>
                        <textarea 
                            class="form-control @error('question') is-invalid @enderror" 
                            id="question" 
                            name="question" 
                            rows="3"
                            placeholder="Enter the quiz question..."
                            required
                        >{{ old('question') }}</textarea>
                        @error('question')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Minimum 10 characters, maximum 1000 characters</div>
                    </div>

                    <!-- Options -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="option_a" class="form-label">Option A <span class="text-danger">*</span></label>
                            <input 
                                type="text" 
                                class="form-control @error('option_a') is-invalid @enderror" 
                                id="option_a" 
                                name="option_a" 
                                placeholder="Enter option A"
                                value="{{ old('option_a') }}"
                                required
                            />
                            @error('option_a')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="option_b" class="form-label">Option B <span class="text-danger">*</span></label>
                            <input 
                                type="text" 
                                class="form-control @error('option_b') is-invalid @enderror" 
                                id="option_b" 
                                name="option_b" 
                                placeholder="Enter option B"
                                value="{{ old('option_b') }}"
                                required
                            />
                            @error('option_b')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="option_c" class="form-label">Option C <span class="text-danger">*</span></label>
                            <input 
                                type="text" 
                                class="form-control @error('option_c') is-invalid @enderror" 
                                id="option_c" 
                                name="option_c" 
                                placeholder="Enter option C"
                                value="{{ old('option_c') }}"
                                required
                            />
                            @error('option_c')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="option_d" class="form-label">Option D <span class="text-danger">*</span></label>
                            <input 
                                type="text" 
                                class="form-control @error('option_d') is-invalid @enderror" 
                                id="option_d" 
                                name="option_d" 
                                placeholder="Enter option D"
                                value="{{ old('option_d') }}"
                                required
                            />
                            @error('option_d')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Correct Answer -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="correct_answer" class="form-label">Correct Answer <span class="text-danger">*</span></label>
                            <select 
                                class="form-select @error('correct_answer') is-invalid @enderror" 
                                id="correct_answer" 
                                name="correct_answer"
                                required
                            >
                                <option value="">Select correct answer</option>
                                <option value="A" {{ old('correct_answer') === 'A' ? 'selected' : '' }}>A</option>
                                <option value="B" {{ old('correct_answer') === 'B' ? 'selected' : '' }}>B</option>
                                <option value="C" {{ old('correct_answer') === 'C' ? 'selected' : '' }}>C</option>
                                <option value="D" {{ old('correct_answer') === 'D' ? 'selected' : '' }}>D</option>
                            </select>
                            @error('correct_answer')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select 
                                class="form-select @error('status') is-invalid @enderror" 
                                id="status" 
                                name="status"
                                required
                            >
                                <option value="">Select status</option>
                                <option value="Active" {{ old('status', 'Active') === 'Active' ? 'selected' : '' }}>Active</option>
                                <option value="Inactive" {{ old('status') === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="card bg-light mb-4" id="questionPreview" style="display: none;">
                        <div class="card-header">
                            <h6 class="mb-0">Question Preview</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-3" id="previewQuestion"></p>
                            <div class="row" id="previewOptions">
                                <!-- Options will be populated by JavaScript -->
                            </div>
                            <div class="mt-3">
                                <strong>Correct Answer: <span id="previewCorrectAnswer" class="text-success"></span></strong>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('administration.quiz.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-check me-1"></i>Create Question
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Live preview functionality
    function updatePreview() {
        const question = $('#question').val();
        const optionA = $('#option_a').val();
        const optionB = $('#option_b').val();
        const optionC = $('#option_c').val();
        const optionD = $('#option_d').val();
        const correctAnswer = $('#correct_answer').val();

        if (question && optionA && optionB && optionC && optionD) {
            $('#previewQuestion').text(question);
            
            const options = [
                { key: 'A', value: optionA },
                { key: 'B', value: optionB },
                { key: 'C', value: optionC },
                { key: 'D', value: optionD }
            ];

            let optionsHtml = '';
            options.forEach(option => {
                const isCorrect = option.key === correctAnswer ? 'border-success bg-light-success' : '';
                optionsHtml += `
                    <div class="col-md-6 mb-2">
                        <div class="p-2 border rounded ${isCorrect}">
                            <span class="badge bg-primary me-2">${option.key}</span>
                            ${option.value}
                        </div>
                    </div>
                `;
            });

            $('#previewOptions').html(optionsHtml);
            $('#previewCorrectAnswer').text(correctAnswer ? `Option ${correctAnswer}` : 'Not selected');
            $('#questionPreview').show();
        } else {
            $('#questionPreview').hide();
        }
    }

    // Bind events for live preview
    $('#question, #option_a, #option_b, #option_c, #option_d, #correct_answer').on('input change', updatePreview);

    // Initial preview update
    updatePreview();
});
</script>
@endpush
