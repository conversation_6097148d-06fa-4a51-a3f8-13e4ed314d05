<?php

namespace App\Services\Administration\Quiz;

use Exception;
use App\Models\Quiz\QuizQuestion;
use App\Models\Quiz\QuizSession;

class QuizService
{
    /**
     * Store a new quiz question
     */
    public function storeQuestion(array $data): QuizQuestion
    {
        $question = QuizQuestion::create([
            'question' => $data['question'],
            'option_a' => $data['option_a'],
            'option_b' => $data['option_b'],
            'option_c' => $data['option_c'],
            'option_d' => $data['option_d'],
            'correct_answer' => $data['correct_answer'],
            'status' => $data['status'],
            'creator_id' => auth()->id(),
        ]);

        return $question;
    }

    /**
     * Update a quiz question
     */
    public function updateQuestion(QuizQuestion $question, array $data): QuizQuestion
    {
        $question->update([
            'question' => $data['question'],
            'option_a' => $data['option_a'],
            'option_b' => $data['option_b'],
            'option_c' => $data['option_c'],
            'option_d' => $data['option_d'],
            'correct_answer' => $data['correct_answer'],
            'status' => $data['status'],
        ]);

        return $question;
    }

    /**
     * Get questions with optional filtering
     */
    public function getQuestionsQuery($request = null)
    {
        $query = QuizQuestion::with(['creator']);

        if ($request) {
            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Filter by creator
            if ($request->filled('creator_id')) {
                $query->where('creator_id', $request->creator_id);
            }

            // Search in question text
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('question', 'like', "%{$search}%")
                      ->orWhere('option_a', 'like', "%{$search}%")
                      ->orWhere('option_b', 'like', "%{$search}%")
                      ->orWhere('option_c', 'like', "%{$search}%")
                      ->orWhere('option_d', 'like', "%{$search}%");
                });
            }

            // Filter by date range
            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }
        }

        return $query->orderByDesc('created_at');
    }

    /**
     * Get quiz sessions with optional filtering
     */
    public function getSessionsQuery($request = null)
    {
        $query = QuizSession::with(['answers.question']);

        if ($request) {
            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Search by candidate name or email
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('candidate_name', 'like', "%{$search}%")
                      ->orWhere('candidate_email', 'like', "%{$search}%");
                });
            }

            // Filter by date range
            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }
        }

        return $query->orderByDesc('created_at');
    }

    /**
     * Delete a quiz question
     */
    public function deleteQuestion(QuizQuestion $question): bool
    {
        // Check if question is used in any active sessions
        $activeSessionsCount = QuizSession::where('status', 'Active')
            ->whereJsonContains('question_ids', $question->id)
            ->count();

        if ($activeSessionsCount > 0) {
            throw new Exception('Cannot delete question as it is being used in active quiz sessions.');
        }

        return $question->delete();
    }

    /**
     * Get statistics for dashboard
     */
    public function getStatistics(): array
    {
        return [
            'total_questions' => QuizQuestion::count(),
            'active_questions' => QuizQuestion::where('status', 'Active')->count(),
            'total_sessions' => QuizSession::count(),
            'completed_sessions' => QuizSession::where('status', 'Completed')->count(),
            'active_sessions' => QuizSession::where('status', 'Active')->count(),
            'average_score' => QuizSession::where('status', 'Completed')->avg('total_score') ?? 0,
        ];
    }
}
