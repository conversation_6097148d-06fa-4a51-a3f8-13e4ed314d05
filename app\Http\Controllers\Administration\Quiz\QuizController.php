<?php

namespace App\Http\Controllers\Administration\Quiz;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\Quiz\QuizQuestion;
use App\Models\Quiz\QuizSession;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Services\Administration\Quiz\QuizService;
use App\Http\Requests\Administration\Quiz\QuizQuestionStoreRequest;
use App\Http\Requests\Administration\Quiz\QuizQuestionUpdateRequest;

class QuizController extends Controller
{
    protected $quizService;

    public function __construct(QuizService $quizService)
    {
        $this->quizService = $quizService;
    }

    /**
     * Display a listing of quiz questions.
     */
    public function index(Request $request)
    {
        $questions = $this->quizService->getQuestionsQuery($request)->paginate(15);
        
        // Get creators for filtering
        $creators = User::whereHas('createdQuizQuestions')
            ->select(['id', 'name'])
            ->get();

        $statistics = $this->quizService->getStatistics();

        return view('administration.quiz.index', compact(['questions', 'creators', 'statistics']));
    }

    /**
     * Show the form for creating a new quiz question.
     */
    public function create()
    {
        return view('administration.quiz.create');
    }

    /**
     * Store a newly created quiz question.
     */
    public function store(QuizQuestionStoreRequest $request)
    {
        try {
            $question = $this->quizService->storeQuestion($request->validated());

            toast('Quiz Question Created Successfully.', 'success');
            return redirect()->route('administration.quiz.show', ['quiz' => $question]);
        } catch (Exception $e) {
            alert('Error!', $e->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified quiz question.
     */
    public function show(QuizQuestion $quiz)
    {
        $quiz->load(['creator']);
        return view('administration.quiz.show', compact('quiz'));
    }

    /**
     * Show the form for editing the specified quiz question.
     */
    public function edit(QuizQuestion $quiz)
    {
        return view('administration.quiz.edit', compact('quiz'));
    }

    /**
     * Update the specified quiz question.
     */
    public function update(QuizQuestionUpdateRequest $request, QuizQuestion $quiz)
    {
        try {
            $this->quizService->updateQuestion($quiz, $request->validated());

            toast('Quiz Question Updated Successfully.', 'success');
            return redirect()->route('administration.quiz.show', ['quiz' => $quiz]);
        } catch (Exception $e) {
            alert('Error!', $e->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified quiz question.
     */
    public function destroy(QuizQuestion $quiz)
    {
        try {
            $this->quizService->deleteQuestion($quiz);

            toast('Quiz Question Deleted Successfully.', 'success');
            return redirect()->route('administration.quiz.index');
        } catch (Exception $e) {
            alert('Error!', $e->getMessage(), 'error');
            return redirect()->back();
        }
    }

    /**
     * Display quiz sessions.
     */
    public function sessions(Request $request)
    {
        $sessions = $this->quizService->getSessionsQuery($request)->paginate(15);
        $statistics = $this->quizService->getStatistics();

        return view('administration.quiz.sessions', compact(['sessions', 'statistics']));
    }

    /**
     * Show session details.
     */
    public function sessionShow(QuizSession $session)
    {
        $session->load(['answers.question']);
        return view('administration.quiz.session-show', compact('session'));
    }
}
