<?php

namespace App\Http\Controllers\Quiz;

use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Quiz\QuizCandidateService;
use App\Http\Requests\Quiz\QuizStartRequest;
use App\Http\Requests\Quiz\QuizAnswerRequest;

class QuizCandidateController extends Controller
{
    protected $quizCandidateService;

    public function __construct(QuizCandidateService $quizCandidateService)
    {
        $this->quizCandidateService = $quizCandidateService;
    }

    /**
     * Show the quiz start page
     */
    public function start()
    {
        return view('quiz.start');
    }

    /**
     * Start a new quiz session
     */
    public function startQuiz(QuizStartRequest $request)
    {
        try {
            $session = $this->quizCandidateService->createSession($request->validated());

            // Store session info in cookies for persistence
            cookie()->queue('quiz_session_id', $session->session_id, 60);
            cookie()->queue('candidate_name', $session->candidate_name, 60);
            cookie()->queue('candidate_email', $session->candidate_email, 60);

            return redirect()->route('quiz.quiz', ['session' => $session->session_id]);
        } catch (Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the quiz interface
     */
    public function quiz(Request $request, string $session)
    {
        try {
            // Check if session is expired
            $this->quizCandidateService->checkAndMarkExpiredSession($session);

            $quizSession = $this->quizCandidateService->getSession($session);
            
            if (!$quizSession) {
                return redirect()->route('quiz.start')
                    ->withErrors(['error' => 'Session not found. Please start a new quiz.']);
            }

            if (!$quizSession->isActive()) {
                return redirect()->route('quiz.result', ['session' => $session]);
            }

            $questions = $this->quizCandidateService->getSessionQuestions($quizSession);
            $remainingTime = $quizSession->getRemainingTimeInSeconds();

            return view('quiz.quiz', compact(['quizSession', 'questions', 'remainingTime']));
        } catch (Exception $e) {
            return redirect()->route('quiz.start')
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Submit an answer via AJAX
     */
    public function submitAnswer(QuizAnswerRequest $request)
    {
        try {
            $result = $this->quizCandidateService->submitAnswer($request->validated());
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Complete the quiz
     */
    public function complete(Request $request)
    {
        try {
            $sessionId = $request->input('session_id');
            
            if (!$sessionId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Session ID is required.'
                ], 400);
            }

            $session = $this->quizCandidateService->completeSession($sessionId);
            
            return response()->json([
                'success' => true,
                'redirect_url' => route('quiz.result', ['session' => $sessionId])
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Show quiz results
     */
    public function result(string $session)
    {
        try {
            $quizSession = $this->quizCandidateService->getSession($session);
            
            if (!$quizSession) {
                return redirect()->route('quiz.start')
                    ->withErrors(['error' => 'Session not found.']);
            }

            if ($quizSession->status === 'Active') {
                // Auto-complete if still active
                $this->quizCandidateService->completeSession($session);
                $quizSession = $quizSession->fresh();
            }

            $quizSession->load(['answers.question']);

            return view('quiz.result', compact('quizSession'));
        } catch (Exception $e) {
            return redirect()->route('quiz.start')
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Check session status via AJAX
     */
    public function checkSession(Request $request)
    {
        try {
            $sessionId = $request->input('session_id');
            
            if (!$sessionId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Session ID is required.'
                ], 400);
            }

            // Check if session is expired
            $isExpired = $this->quizCandidateService->checkAndMarkExpiredSession($sessionId);
            
            if ($isExpired) {
                return response()->json([
                    'success' => false,
                    'expired' => true,
                    'redirect_url' => route('quiz.result', ['session' => $sessionId])
                ]);
            }

            $session = $this->quizCandidateService->getSession($sessionId);
            
            if (!$session || !$session->isActive()) {
                return response()->json([
                    'success' => false,
                    'expired' => true,
                    'redirect_url' => route('quiz.result', ['session' => $sessionId])
                ]);
            }

            return response()->json([
                'success' => true,
                'remaining_time' => $session->getRemainingTimeInSeconds()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
