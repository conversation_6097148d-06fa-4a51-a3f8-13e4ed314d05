<?php

namespace App\Models\Quiz;

use App\Traits\HasCustomRouteId;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use App\Models\Quiz\Relations\QuizQuestionRelations;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class QuizQuestion extends Model
{
    use HasFactory, SoftDeletes, CascadeSoftDeletes, HasCustomRouteId;

    // Relations
    use QuizQuestionRelations;

    protected $cascadeDeletes = ['answers'];

    // Mass assignable attributes
    protected $fillable = [
        'question',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'status',
        'creator_id',
    ];

    // Casting attributes
    protected $casts = [
        'question' => 'string',
        'option_a' => 'string',
        'option_b' => 'string',
        'option_c' => 'string',
        'option_d' => 'string',
        'correct_answer' => 'string',
        'status' => 'string',
    ];

    /**
     * Get the available answer options
     */
    public static function getAnswerOptions(): array
    {
        return ['A', 'B', 'C', 'D'];
    }

    /**
     * Get the status options
     */
    public static function getStatusOptions(): array
    {
        return ['Active', 'Inactive'];
    }

    /**
     * Scope to get only active questions
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    /**
     * Get the options as an array
     */
    public function getOptionsAttribute(): array
    {
        return [
            'A' => $this->option_a,
            'B' => $this->option_b,
            'C' => $this->option_c,
            'D' => $this->option_d,
        ];
    }

    /**
     * Check if the given answer is correct
     */
    public function isCorrectAnswer(string $answer): bool
    {
        return strtoupper($answer) === strtoupper($this->correct_answer);
    }
}
