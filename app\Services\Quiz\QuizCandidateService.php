<?php

namespace App\Services\Quiz;

use Exception;
use Illuminate\Support\Str;
use App\Models\Quiz\QuizAnswer;
use App\Models\Quiz\QuizSession;
use App\Models\Quiz\QuizQuestion;
use Illuminate\Support\Facades\Cache;

class QuizCandidateService
{
    /**
     * Create a new quiz session
     */
    public function createSession(array $data): QuizSession
    {
        // Generate unique session ID
        $sessionId = $this->generateUniqueSessionId();

        // Get 10 random active questions
        $questions = QuizQuestion::active()
            ->inRandomOrder()
            ->limit(10)
            ->pluck('id')
            ->toArray();

        if (count($questions) < 10) {
            throw new Exception('Not enough active questions available. Please contact administrator.');
        }

        // Create session
        $session = QuizSession::create([
            'session_id' => $sessionId,
            'candidate_name' => $data['candidate_name'],
            'candidate_email' => $data['candidate_email'],
            'question_ids' => $questions,
            'total_score' => 0,
            'status' => 'Active',
            'started_at' => now(),
            'expires_at' => now()->addMinutes(10), // 10 minutes timer
        ]);

        // Cache the session for quick access
        $this->cacheSession($session);

        return $session;
    }

    /**
     * Get session from cache or database
     */
    public function getSession(string $sessionId): ?QuizSession
    {
        // Try to get from cache first
        $cacheKey = "quiz_session_{$sessionId}";
        $session = Cache::get($cacheKey);

        if (!$session) {
            // Get from database and cache it
            $session = QuizSession::where('session_id', $sessionId)->first();
            if ($session) {
                $this->cacheSession($session);
            }
        }

        return $session;
    }

    /**
     * Get questions for a session
     */
    public function getSessionQuestions(QuizSession $session): array
    {
        $cacheKey = "quiz_questions_{$session->session_id}";
        
        $questions = Cache::remember($cacheKey, 600, function() use ($session) {
            return QuizQuestion::whereIn('id', $session->question_ids)
                ->get()
                ->map(function($question) {
                    return [
                        'id' => $question->id,
                        'question' => $question->question,
                        'options' => $question->options,
                    ];
                })
                ->toArray();
        });

        return $questions;
    }

    /**
     * Submit an answer
     */
    public function submitAnswer(array $data): array
    {
        $session = $this->getSession($data['session_id']);
        
        if (!$session || !$session->isActive()) {
            throw new Exception('Invalid or expired session.');
        }

        $question = QuizQuestion::find($data['question_id']);
        if (!$question) {
            throw new Exception('Question not found.');
        }

        // Check if question belongs to this session
        if (!in_array($question->id, $session->question_ids)) {
            throw new Exception('Question does not belong to this session.');
        }

        // Check if answer already exists
        $existingAnswer = QuizAnswer::where('quiz_session_id', $session->id)
            ->where('quiz_question_id', $question->id)
            ->first();

        $isCorrect = $question->isCorrectAnswer($data['selected_answer']);
        $score = $isCorrect ? 1 : -1;

        if ($existingAnswer) {
            // Update existing answer
            $oldScore = $existingAnswer->score;
            $existingAnswer->update([
                'selected_answer' => $data['selected_answer'],
                'is_correct' => $isCorrect,
                'score' => $score,
            ]);

            // Update session total score
            $session->increment('total_score', $score - $oldScore);
        } else {
            // Create new answer
            QuizAnswer::create([
                'quiz_session_id' => $session->id,
                'quiz_question_id' => $question->id,
                'selected_answer' => $data['selected_answer'],
                'is_correct' => $isCorrect,
                'score' => $score,
            ]);

            // Update session total score
            $session->increment('total_score', $score);
        }

        // Update cache
        $this->cacheSession($session->fresh());

        return [
            'success' => true,
            'is_correct' => $isCorrect,
            'score' => $score,
            'total_score' => $session->fresh()->total_score,
        ];
    }

    /**
     * Complete a quiz session
     */
    public function completeSession(string $sessionId): QuizSession
    {
        $session = $this->getSession($sessionId);
        
        if (!$session) {
            throw new Exception('Session not found.');
        }

        $session->markAsCompleted();
        
        // Clear cache
        $this->clearSessionCache($sessionId);

        return $session;
    }

    /**
     * Check if session is expired and mark it
     */
    public function checkAndMarkExpiredSession(string $sessionId): bool
    {
        $session = $this->getSession($sessionId);
        
        if (!$session) {
            return false;
        }

        if ($session->isExpired() && $session->status === 'Active') {
            $session->markAsExpired();
            $this->clearSessionCache($sessionId);
            return true;
        }

        return false;
    }

    /**
     * Generate unique session ID
     */
    private function generateUniqueSessionId(): string
    {
        do {
            $sessionId = 'quiz_' . Str::random(16) . '_' . time();
        } while (QuizSession::where('session_id', $sessionId)->exists());

        return $sessionId;
    }

    /**
     * Cache session data
     */
    private function cacheSession(QuizSession $session): void
    {
        $cacheKey = "quiz_session_{$session->session_id}";
        Cache::put($cacheKey, $session, 600); // Cache for 10 minutes
    }

    /**
     * Clear session cache
     */
    private function clearSessionCache(string $sessionId): void
    {
        $cacheKey = "quiz_session_{$sessionId}";
        Cache::forget($cacheKey);
        
        $questionsCacheKey = "quiz_questions_{$sessionId}";
        Cache::forget($questionsCacheKey);
    }
}
