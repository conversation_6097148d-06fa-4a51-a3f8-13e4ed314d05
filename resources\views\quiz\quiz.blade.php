@extends('layouts.quiz')

@section('page_title', 'Quiz in Progress')

@push('styles')
<style>
.quiz-timer {
    font-size: 1.5rem;
    font-weight: bold;
}
.quiz-timer.warning {
    color: #ff6b35;
}
.quiz-timer.danger {
    color: #dc3545;
    animation: pulse 1s infinite;
}
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
.question-card {
    border: 2px solid #e7eaf3;
    transition: all 0.3s ease;
}
.question-card.answered {
    border-color: #28a745;
    background-color: #f8fff9;
}
.option-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e7eaf3;
}
.option-card:hover {
    border-color: #696cff;
    background-color: #f8f9ff;
}
.option-card.selected {
    border-color: #696cff;
    background-color: #696cff;
    color: white;
}
.progress-indicator {
    height: 8px;
    background-color: #e7eaf3;
    border-radius: 4px;
    overflow: hidden;
}
.progress-bar-custom {
    height: 100%;
    background-color: #28a745;
    transition: width 0.3s ease;
}
</style>
@endpush

@section('content')
<div class="row">
    <!-- Quiz Header -->
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <h5 class="mb-0">{{ $quizSession->candidate_name }}</h5>
                        <small class="text-muted">{{ $quizSession->candidate_email }}</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="quiz-timer" id="timer">10:00</div>
                        <small class="text-muted">Time Remaining</small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="progress-indicator">
                            <div class="progress-bar-custom" id="progressBar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted mt-1 d-block">
                            <span id="answeredCount">0</span> of {{ count($questions) }} answered
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Questions -->
    <div class="col-12">
        @foreach($questions as $index => $question)
        <div class="card question-card mb-4" id="question-{{ $question['id'] }}" data-question-id="{{ $question['id'] }}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="card-title mb-0">Question {{ $index + 1 }}</h6>
                    <span class="badge bg-light text-dark">{{ $index + 1 }}/{{ count($questions) }}</span>
                </div>
                
                <p class="card-text mb-4">{{ $question['question'] }}</p>
                
                <div class="row">
                    @foreach($question['options'] as $optionKey => $optionValue)
                    <div class="col-md-6 mb-3">
                        <div class="option-card p-3 rounded" data-option="{{ $optionKey }}" data-question-id="{{ $question['id'] }}">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-3">{{ $optionKey }}</span>
                                <span>{{ $optionValue }}</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Submit Button -->
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <button type="button" class="btn btn-success btn-lg" id="submitQuizBtn">
                    <i class="ti ti-check me-2"></i>Submit Quiz
                </button>
                <p class="text-muted mt-2 mb-0">
                    Make sure you have answered all questions before submitting.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for session data -->
<input type="hidden" id="sessionId" value="{{ $quizSession->session_id }}">
<input type="hidden" id="remainingTime" value="{{ $remainingTime }}">
@endsection

@push('scripts')
<script src="{{ asset('assets/js/custom_js/quiz/quiz-timer.js') }}"></script>
<script src="{{ asset('assets/js/custom_js/quiz/quiz-candidate.js') }}"></script>
@endpush
